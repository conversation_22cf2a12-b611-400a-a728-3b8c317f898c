// Script to create demo brand user for testing
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const dotenv = require('dotenv');

dotenv.config();

const { User } = require('./database');

async function createDemoUser() {
  try {
    console.log('🔍 Connecting to database...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    // Demo user credentials with secure passwords
    const demoUsers = [
      {
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>',
        email: '<EMAIL>',
        password: 'SecurePass2024!@#$',
        isGoogleUser: false,
        isVerified: true
      },
      {
        name: 'Demo Brand User',
        email: '<EMAIL>',
        password: 'DemoUser2024!@#',
        isGoogleUser: false,
        isVerified: true
      },
      {
        name: 'Test Brand',
        email: '<EMAIL>',
        password: 'TestBrand2024!@#',
        isGoogleUser: false,
        isVerified: true
      },
      {
        name: 'Brand Manager',
        email: '<EMAIL>',
        password: 'BrandManager2024!@#',
        isGoogleUser: false,
        isVerified: true
      }
    ];

    for (const userData of demoUsers) {
      console.log(`\n🔍 Checking user: ${userData.email}`);
      
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      
      if (existingUser) {
        console.log(`✅ User ${userData.email} already exists`);
        
        // Update password if needed
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        existingUser.password = hashedPassword;
        existingUser.isVerified = true;
        await existingUser.save();
        
        console.log(`🔐 Password updated for ${userData.email}`);
      } else {
        // Create new user
        console.log(`🆕 Creating new user: ${userData.email}`);
        
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        
        const newUser = new User({
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          isGoogleUser: userData.isGoogleUser,
          isVerified: userData.isVerified,
          blocked: false
        });
        
        await newUser.save();
        console.log(`✅ Created user: ${userData.email}`);
      }
    }

    console.log('\n🎉 Demo users ready!');
    console.log('==========================================');
    console.log('📋 DEMO BRAND CREDENTIALS (SECURE):');
    console.log('==========================================');
    console.log('');
    console.log('👤 PRIMARY USER:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: SecurePass2024!@#$');
    console.log('');
    console.log('👤 Demo User 1:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: DemoUser2024!@#');
    console.log('');
    console.log('👤 Demo User 2:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: TestBrand2024!@#');
    console.log('');
    console.log('👤 Demo User 3:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: BrandManager2024!@#');
    console.log('');
    console.log('🔗 Login URL: http://localhost:3000/brand-auth');
    console.log('==========================================');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

createDemoUser();
