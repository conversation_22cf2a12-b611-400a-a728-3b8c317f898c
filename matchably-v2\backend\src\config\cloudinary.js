import { v2 as cloudinary } from 'cloudinary';
import { logger } from '../utils/logger.js';

/**
 * Configure Cloudinary
 */
export const cloudinaryConfig = () => {
  try {
    const { CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, CLOUDINARY_API_SECRET } = process.env;
    
    if (!CLOUDINARY_CLOUD_NAME || !CLOUDINARY_API_KEY || !CLOUDINARY_API_SECRET) {
      throw new Error('Missing Cloudinary configuration. Please check your environment variables.');
    }
    
    cloudinary.config({
      cloud_name: CLOUDINARY_CLOUD_NAME,
      api_key: CLOUDINARY_API_KEY,
      api_secret: CLOUDINARY_API_SECRET,
      secure: true,
    });
    
    logger.info('☁️ Cloudinary configured successfully');
    
  } catch (error) {
    logger.error('❌ Cloudinary configuration failed:', error.message);
    throw error;
  }
};

/**
 * Upload image to Cloudinary
 * @param {string} filePath - Path to the file
 * @param {object} options - Upload options
 * @returns {Promise<object>} Upload result
 */
export const uploadImage = async (filePath, options = {}) => {
  try {
    const defaultOptions = {
      folder: 'matchably-v2',
      resource_type: 'image',
      quality: 'auto',
      fetch_format: 'auto',
      flags: 'progressive',
      ...options,
    };
    
    const result = await cloudinary.uploader.upload(filePath, defaultOptions);
    
    return {
      success: true,
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
    };
    
  } catch (error) {
    logger.error('❌ Cloudinary upload failed:', error.message);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Upload multiple images to Cloudinary
 * @param {Array} filePaths - Array of file paths
 * @param {object} options - Upload options
 * @returns {Promise<Array>} Array of upload results
 */
export const uploadMultipleImages = async (filePaths, options = {}) => {
  try {
    const uploadPromises = filePaths.map(filePath => uploadImage(filePath, options));
    const results = await Promise.allSettled(uploadPromises);
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        logger.error(`Upload failed for file ${index}:`, result.reason);
        return {
          success: false,
          error: result.reason.message,
        };
      }
    });
    
  } catch (error) {
    logger.error('❌ Multiple upload failed:', error.message);
    throw error;
  }
};

/**
 * Delete image from Cloudinary
 * @param {string} publicId - Public ID of the image
 * @returns {Promise<object>} Deletion result
 */
export const deleteImage = async (publicId) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    
    return {
      success: result.result === 'ok',
      result: result.result,
    };
    
  } catch (error) {
    logger.error('❌ Cloudinary deletion failed:', error.message);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Generate optimized image URL
 * @param {string} publicId - Public ID of the image
 * @param {object} transformations - Image transformations
 * @returns {string} Optimized image URL
 */
export const getOptimizedImageUrl = (publicId, transformations = {}) => {
  const defaultTransformations = {
    quality: 'auto',
    fetch_format: 'auto',
    flags: 'progressive',
    ...transformations,
  };
  
  return cloudinary.url(publicId, defaultTransformations);
};

/**
 * Generate image variants (thumbnails, different sizes)
 * @param {string} publicId - Public ID of the image
 * @returns {object} Object with different image variants
 */
export const generateImageVariants = (publicId) => {
  return {
    thumbnail: getOptimizedImageUrl(publicId, { width: 150, height: 150, crop: 'fill' }),
    small: getOptimizedImageUrl(publicId, { width: 300, height: 300, crop: 'limit' }),
    medium: getOptimizedImageUrl(publicId, { width: 600, height: 600, crop: 'limit' }),
    large: getOptimizedImageUrl(publicId, { width: 1200, height: 1200, crop: 'limit' }),
    original: getOptimizedImageUrl(publicId),
  };
};

// Export cloudinary instance for direct access
export { cloudinary };
