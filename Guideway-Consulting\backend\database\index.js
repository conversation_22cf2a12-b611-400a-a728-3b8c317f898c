const mongoose = require("mongoose");
const validator = require("validator");
const dotenv = require("dotenv");
dotenv.config();

const MONGO_URL = process.env.MONGO_URL;

mongoose.connect(MONGO_URL);

// ✅ Updated User Schema
const UserShema = mongoose.Schema({
  name: {
    type: String,
    required: [true, "Please provide your name"],
    trim: true,
  },
  email: {
    type: String,
    required: [true, "Please provide your email"],
    unique: true,
    lowercase: true,
    validate: [validator.isEmail, "Please provide a valid email"],
  },
  password: {
    type: String,
    minlength: 8,
    // not required for Google users
  },
  isGoogleUser: {
    type: Boolean,
    default: false,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  blocked: {
    type: Boolean,
    default: false,
  },
  appliedCampaigns: [
    {
      type: [mongoose.Schema.Types.ObjectId],
      ref: "AppliedCampaigns",
    },
  ],
  // ✅ Newly added social media fields
  instagramId: {
    type: String,
    default: "",
    trim: true,
  },
  youtubeId: {
    type: String,
    default: "",
    trim: true,
  },
  tiktokId: {
    type: String,
    default: "",
    trim: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});


const CampaignSchema = mongoose.Schema({
  campaignIndustry: {
    type: String,
    required: [true, "Campaign industry is required"],
    trim: true,
  },
  campaignTitle: {
    type: String,
    required: [true, "Campaign title is required"],
    trim: true,
  },
  brandName: {
    type: String,
    required: [true, "Brand name is required"],
    trim: true,
  },
  productDescription: {
    type: String,
    required: [true, "Product description is required"],
    trim: true,
  },
  brandLogo: {
    type: String,
    default: "",
  },
  productImages: {
    type: [String],
    default: [],
  },
  contentFormat: {
    type: [String],
    required: true,
  },
  requiredHashtags: {
    type: [String],
  },
  recruiting: {
    type: Number,
    default: 0,
  },
  influencersReceive: {
    type: String,
    required: [true, "Offer details are required"],
  },
  deadline: {
    type: Date,
    required: [true, "Deadline is required"],
    validate: [validator.isDate, "Invalid date format"],
  },
  participationRequirements: {
    type: String,
    required: [true, "Requirements are required"],
  },

  // ✅ New fields
  recruitmentEndDate: {
    type: Date,
    required: [true, "Recruitment end date is required"],
    validate: [validator.isDate, "Invalid recruitment end date"],
  },
  status: {
    type: String,
    enum: ["Active", "Deactive"],
    default: "Active",
  },
});


const appliedCampaignsSchema = mongoose.Schema({
  name: {
    type: String,
    required: [true, "Please provide your name"],
    trim: true,
  },
  email: {
    type: String,
    required: [true, "Please provide your email"],
    lowercase: true,
    validate: [validator.isEmail, "Please provide a valid email"],
  },
  phone: {
    type: String,
    required: [true, "Please enter your phone number"],
    trim: true,
  },
  address: {
    type: String,
    required: [true, "Please enter your address"],
    trim: true,
  },
  city: {
    type: String,
    required: [true, "Please provide your city"],
    trim: true,
  },
  state: {
    type: String,
    required: [true, "Please select a state"],
    trim: true,
    uppercase: true,
  },
  zipCode: {
    type: String,
    required: [true, "Please provide your zip code"],
    validate: {
      validator: function (v) {
        return /^\d{5}(-\d{4})?$/.test(v);
      },
      message: "Please enter a valid ZIP code",
    },
  },

  // ✅ New social fields
  instagramId: {
    type: String,
    default: "",
    trim: true,
  },
  tiktokId: {
    type: String,
    default: "",
    trim: true,
  },

  status: {
    type: String,
    enum: ["Pending", "Approved", "Rejected"],
    default: "Pending",
  },
  rejectionReason: {
    type: String,
    default: "",
  },
  showReasonToInfluencer: {
    type: Boolean,
    default: false,
  },
  postUrl: {
    type: String,
    default: "",
  },
  allowReuse: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  campaign: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Campaigns",
  },
});


const campaignSubmissionSchema = new mongoose.Schema(
  {
    campaign_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Campaign',
      required: true,
    },
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      validate: [validator.isEmail, 'Please provide a valid email'],
    },
    instagram_urls: {
      type: [String],
      default: [],
    },
    tiktok_urls: {
      type: [String],
      default: [],
    },
    allow_brand_reuse: {
      type: Boolean,
      required: true,
    },
    status: {
      type: String,
      enum: ['submitted', 'reviewed', 'rejected'],
      default: 'submitted',
    },
  },
  {
    timestamps: {
      createdAt: 'submitted_at',
      updatedAt: 'updated_at',
    },
  }
);


// ✅ Creator Performance Schema for Trust Metric
const CreatorPerformanceSchema = mongoose.Schema({
  creatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  campaignId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Campaigns",
    required: true,
  },
  trackingInputDate: {
    type: Date,
    default: null,
  },
  contentSubmissionDate: {
    type: Date,
    default: null,
  },
  isOnTime: {
    type: Boolean,
    default: null, // null = not yet determined, true = on time, false = late
  },
  status: {
    type: String,
    enum: ['Applied', 'Approved', 'Rejected', 'Not Submitted', 'Submitted'],
    default: 'Applied',
  },
}, {
  timestamps: true,
});

// ✅ Index for efficient queries
CreatorPerformanceSchema.index({ creatorId: 1, campaignId: 1 }, { unique: true });
CreatorPerformanceSchema.index({ creatorId: 1 });

const User = mongoose.model("User", UserShema);
const Campaign = mongoose.model("Campaigns", CampaignSchema);
const appliedCampaigns = mongoose.model("AppliedCampaigns", appliedCampaignsSchema);
const campaignSubmission = mongoose.model("CampaignSubmission", campaignSubmissionSchema);
const CreatorPerformance = mongoose.model("CreatorPerformance", CreatorPerformanceSchema);

module.exports = {
  User,
  Campaign,
  appliedCampaigns,
  campaignSubmission,
  CreatorPerformance
};
