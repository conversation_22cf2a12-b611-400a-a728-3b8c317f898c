import mongoose from 'mongoose';
import validator from 'validator';

const { Schema } = mongoose;

// Campaign schema with comprehensive structure
const campaignSchema = new Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, 'Campaign title is required'],
    trim: true,
    minlength: [5, 'Title must be at least 5 characters long'],
    maxlength: [100, 'Title cannot exceed 100 characters'],
  },
  
  description: {
    type: String,
    required: [true, 'Campaign description is required'],
    trim: true,
    minlength: [20, 'Description must be at least 20 characters long'],
    maxlength: [2000, 'Description cannot exceed 2000 characters'],
  },
  
  // Brand Information
  brandName: {
    type: String,
    required: [true, 'Brand name is required'],
    trim: true,
    maxlength: [50, 'Brand name cannot exceed 50 characters'],
  },
  
  brandLogo: {
    url: String,
    publicId: String,
  },
  
  // Product Information
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters'],
  },
  
  productImages: [{
    url: String,
    publicId: String,
    caption: String,
  }],
  
  // Campaign Details
  industry: {
    type: String,
    required: [true, 'Industry is required'],
    enum: [
      'Beauty & Skincare',
      'Fashion & Apparel',
      'Health & Wellness',
      'Food & Beverage',
      'Technology',
      'Home & Garden',
      'Sports & Fitness',
      'Travel & Lifestyle',
      'Entertainment',
      'Education',
      'Other'
    ],
  },
  
  // Content Requirements
  contentFormat: [{
    type: String,
    required: true,
    enum: ['Instagram Post', 'Instagram Story', 'Instagram Reel', 'TikTok Video', 'YouTube Video', 'YouTube Short'],
  }],
  
  requiredHashtags: [{
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return v.startsWith('#');
      },
      message: 'Hashtags must start with #',
    },
  }],
  
  mentionHandle: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || v.startsWith('@');
      },
      message: 'Mention handle must start with @',
    },
  },
  
  toneGuide: {
    type: String,
    maxlength: [500, 'Tone guide cannot exceed 500 characters'],
    trim: true,
  },
  
  referenceContent: [{
    url: {
      type: String,
      validate: [validator.isURL, 'Please provide a valid URL'],
    },
    description: String,
  }],
  
  // Participation Requirements
  participationRequirements: {
    type: String,
    required: [true, 'Participation requirements are required'],
    trim: true,
    maxlength: [1000, 'Requirements cannot exceed 1000 characters'],
  },
  
  minFollowers: {
    type: Number,
    min: 0,
    default: 0,
  },
  
  targetAudience: {
    type: String,
    maxlength: [500, 'Target audience description cannot exceed 500 characters'],
    trim: true,
  },
  
  // Campaign Logistics
  creatorCount: {
    type: Number,
    required: [true, 'Number of creators is required'],
    min: [1, 'At least 1 creator is required'],
    max: [1000, 'Cannot exceed 1000 creators'],
  },
  
  budget: {
    type: Number,
    min: 0,
    default: 0,
  },
  
  // What Creators Receive
  compensation: {
    type: {
      type: String,
      enum: ['Product Only', 'Product + Payment', 'Payment Only', 'Commission'],
      default: 'Product Only',
    },
    amount: {
      type: Number,
      min: 0,
      default: 0,
    },
    currency: {
      type: String,
      default: 'USD',
    },
    description: {
      type: String,
      required: [true, 'Compensation description is required'],
      trim: true,
      maxlength: [500, 'Compensation description cannot exceed 500 characters'],
    },
  },
  
  // Important Dates
  applicationDeadline: {
    type: Date,
    required: [true, 'Application deadline is required'],
    validate: {
      validator: function(v) {
        return v > new Date();
      },
      message: 'Application deadline must be in the future',
    },
  },
  
  contentDeadline: {
    type: Date,
    required: [true, 'Content deadline is required'],
    validate: {
      validator: function(v) {
        return v > this.applicationDeadline;
      },
      message: 'Content deadline must be after application deadline',
    },
  },
  
  // Campaign Status
  status: {
    type: String,
    enum: ['Draft', 'Active', 'Paused', 'Completed', 'Cancelled'],
    default: 'Draft',
  },
  
  // Brand/Creator Information
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'Brand',
    required: true,
  },
  
  // Application Management
  applications: [{
    type: Schema.Types.ObjectId,
    ref: 'CampaignApplication',
  }],
  
  selectedCreators: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  
  // Performance Metrics
  metrics: {
    totalApplications: {
      type: Number,
      default: 0,
    },
    approvedApplications: {
      type: Number,
      default: 0,
    },
    rejectedApplications: {
      type: Number,
      default: 0,
    },
    pendingApplications: {
      type: Number,
      default: 0,
    },
    contentSubmissions: {
      type: Number,
      default: 0,
    },
    submissionRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100,
    },
  },
  
  // Admin Fields
  isApproved: {
    type: Boolean,
    default: false,
  },
  
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
  },
  
  approvedAt: Date,
  
  rejectionReason: String,
  
  // SEO and Discovery
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  
  featured: {
    type: Boolean,
    default: false,
  },
  
  priority: {
    type: Number,
    default: 0,
    min: 0,
    max: 10,
  },
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better performance
campaignSchema.index({ status: 1, applicationDeadline: 1 });
campaignSchema.index({ createdBy: 1, status: 1 });
campaignSchema.index({ industry: 1, status: 1 });
campaignSchema.index({ featured: 1, priority: -1 });
campaignSchema.index({ createdAt: -1 });
campaignSchema.index({ 'metrics.submissionRate': -1 });

// Virtual for days until application deadline
campaignSchema.virtual('daysUntilApplicationDeadline').get(function() {
  if (!this.applicationDeadline) return null;
  const now = new Date();
  const deadline = new Date(this.applicationDeadline);
  const diffTime = deadline - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for days until content deadline
campaignSchema.virtual('daysUntilContentDeadline').get(function() {
  if (!this.contentDeadline) return null;
  const now = new Date();
  const deadline = new Date(this.contentDeadline);
  const diffTime = deadline - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for campaign duration
campaignSchema.virtual('campaignDuration').get(function() {
  if (!this.applicationDeadline || !this.contentDeadline) return null;
  const diffTime = new Date(this.contentDeadline) - new Date(this.applicationDeadline);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Pre-save middleware to update metrics
campaignSchema.pre('save', function(next) {
  // Calculate submission rate
  if (this.metrics.approvedApplications > 0) {
    this.metrics.submissionRate = Math.round(
      (this.metrics.contentSubmissions / this.metrics.approvedApplications) * 100
    );
  }
  
  next();
});

// Static method to find active campaigns
campaignSchema.statics.findActive = function() {
  return this.find({
    status: 'Active',
    isApproved: true,
    applicationDeadline: { $gt: new Date() }
  });
};

// Static method to find featured campaigns
campaignSchema.statics.findFeatured = function(limit = 10) {
  return this.find({
    status: 'Active',
    isApproved: true,
    featured: true,
    applicationDeadline: { $gt: new Date() }
  })
  .sort({ priority: -1, createdAt: -1 })
  .limit(limit);
};

// Method to check if applications are open
campaignSchema.methods.isApplicationOpen = function() {
  return this.status === 'Active' && 
         this.isApproved && 
         new Date() < this.applicationDeadline;
};

// Method to update application metrics
campaignSchema.methods.updateApplicationMetrics = function() {
  // This would typically be called when applications change
  // Implementation depends on how applications are structured
  return this.save();
};

export default mongoose.model('Campaign', campaignSchema);
