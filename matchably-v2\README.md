# Matchably V2 - Modern UGC Platform

A modern, redesigned version of the Matchably platform with improved UI/UX, animations, and clean architecture.

## 🚀 Features

### For Creators
- Browse and apply to campaigns
- Track application status
- Submit content with tracking
- Performance scoring system
- Referral rewards program

### For Brands
- Create and manage campaigns
- Review creator applications
- Track campaign performance
- Analytics dashboard
- Payment management

### For Admins
- User management
- Campaign oversight
- System analytics
- Brand approvals
- Performance monitoring

## 🛠 Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **JWT** authentication
- **Cloudinary** for file uploads
- **Nodemailer** for emails
- **Stripe** for payments

### Frontend
- **React 18** with Vite
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Query** for data fetching
- **Zustand** for state management
- **React Hook Form** for forms

## 🎨 Design System

### Color Palette
- **Primary Background**: `#000000` (Black)
- **Secondary Background**: `#141414` (<PERSON> Gray)
- **Accent Primary**: `#E0FFFA` (Soft Mint White)
- **Accent Secondary**: `#7EFCD8` (Mint Highlight)
- **Success**: `#22c55e` (Green)
- **Warning**: `#f59e0b` (Orange)
- **Error**: `#ef4444` (Red)

### Typography
- **Primary Font**: Inter (System UI fallback)
- **Secondary Font**: Noto Sans
- **Accent Font**: Lato

## 📁 Project Structure

```
matchably-v2/
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── utils/
│   │   └── app.js
│   ├── package.json
│   └── server.js
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── store/
│   │   ├── types/
│   │   ├── utils/
│   │   └── App.tsx
│   ├── package.json
│   └── index.html
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd matchably-v2
```

2. Install backend dependencies
```bash
cd backend
npm install
```

3. Install frontend dependencies
```bash
cd ../frontend
npm install
```

4. Set up environment variables
```bash
# Backend .env
cp .env.example .env
# Configure your MongoDB, Cloudinary, and other services
```

5. Start development servers
```bash
# Backend (from backend directory)
npm run dev

# Frontend (from frontend directory)
npm run dev
```

## 🔧 Environment Variables

### Backend (.env)
```
NODE_ENV=development
PORT=5000
MONGO_URL=mongodb://localhost:27017/matchably-v2
JWT_SECRET=your-jwt-secret
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
STRIPE_SECRET_KEY=your-stripe-secret
FRONTEND_URL=http://localhost:3000
```

## 📝 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/verify` - Verify JWT token
- `POST /api/auth/forgot-password` - Password reset

### Campaign Endpoints
- `GET /api/campaigns` - Get all campaigns
- `POST /api/campaigns` - Create campaign (Brand/Admin)
- `GET /api/campaigns/:id` - Get campaign details
- `PUT /api/campaigns/:id` - Update campaign
- `DELETE /api/campaigns/:id` - Delete campaign

### User Endpoints
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/applications` - Get user applications
- `POST /api/users/apply/:campaignId` - Apply to campaign

## 🎯 Key Improvements

### Architecture
- ✅ Clean separation of concerns
- ✅ TypeScript for better type safety
- ✅ Modern React patterns with hooks
- ✅ Improved error handling
- ✅ Better API structure

### UI/UX
- ✅ Modern, responsive design
- ✅ Smooth animations with Framer Motion
- ✅ Glass morphism effects
- ✅ Improved accessibility
- ✅ Better mobile experience

### Performance
- ✅ Optimized bundle size
- ✅ Lazy loading components
- ✅ Efficient state management
- ✅ Image optimization
- ✅ Caching strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
