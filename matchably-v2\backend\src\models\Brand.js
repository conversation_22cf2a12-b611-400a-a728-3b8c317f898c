import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import validator from 'validator';

const { Schema } = mongoose;

// Brand schema for brand accounts
const brandSchema = new Schema({
  // Basic Information
  brandName: {
    type: String,
    required: [true, 'Brand name is required'],
    trim: true,
    minlength: [2, 'Brand name must be at least 2 characters long'],
    maxlength: [100, 'Brand name cannot exceed 100 characters'],
  },
  
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    validate: [validator.isEmail, 'Please provide a valid email address'],
    index: true,
  },
  
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false,
  },
  
  // Contact Information
  contactPerson: {
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true,
      maxlength: [50, 'First name cannot exceed 50 characters'],
    },
    lastName: {
      type: String,
      required: [true, 'Last name is required'],
      trim: true,
      maxlength: [50, 'Last name cannot exceed 50 characters'],
    },
    title: {
      type: String,
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters'],
    },
    phone: {
      type: String,
      validate: {
        validator: function(v) {
          return !v || validator.isMobilePhone(v);
        },
        message: 'Please provide a valid phone number',
      },
    },
  },
  
  // Company Information
  companyInfo: {
    website: {
      type: String,
      validate: {
        validator: function(v) {
          return !v || validator.isURL(v);
        },
        message: 'Please provide a valid website URL',
      },
    },
    industry: {
      type: String,
      required: [true, 'Industry is required'],
      enum: [
        'Beauty & Skincare',
        'Fashion & Apparel',
        'Health & Wellness',
        'Food & Beverage',
        'Technology',
        'Home & Garden',
        'Sports & Fitness',
        'Travel & Lifestyle',
        'Entertainment',
        'Education',
        'Other'
      ],
    },
    companySize: {
      type: String,
      enum: ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'],
    },
    description: {
      type: String,
      maxlength: [1000, 'Description cannot exceed 1000 characters'],
      trim: true,
    },
    logo: {
      url: String,
      publicId: String,
    },
  },
  
  // Address Information
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required'],
      trim: true,
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true,
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true,
    },
    zipCode: {
      type: String,
      required: [true, 'ZIP code is required'],
      validate: {
        validator: function(v) {
          return /^\d{5}(-\d{4})?$/.test(v);
        },
        message: 'Please provide a valid ZIP code',
      },
    },
    country: {
      type: String,
      required: [true, 'Country is required'],
      default: 'US',
    },
  },
  
  // Social Media Presence
  socialMedia: {
    instagram: String,
    tiktok: String,
    youtube: String,
    facebook: String,
    twitter: String,
    linkedin: String,
  },
  
  // Account Status
  isVerified: {
    type: Boolean,
    default: false,
  },
  
  isApproved: {
    type: Boolean,
    default: false,
  },
  
  isBlocked: {
    type: Boolean,
    default: false,
  },
  
  // Subscription Information
  subscription: {
    plan: {
      type: String,
      enum: ['Free', 'Starter', 'Professional', 'Enterprise'],
      default: 'Free',
    },
    status: {
      type: String,
      enum: ['Active', 'Inactive', 'Cancelled', 'Past Due'],
      default: 'Inactive',
    },
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    cancelAtPeriodEnd: {
      type: Boolean,
      default: false,
    },
  },
  
  // Plan Limits and Usage
  planLimits: {
    campaignsAllowed: {
      type: Number,
      default: 1, // Free plan allows 1 campaign
    },
    extraCampaignsAllowed: {
      type: Number,
      default: 0,
    },
    campaignsUsed: {
      type: Number,
      default: 0,
    },
    monthlyReset: {
      type: Boolean,
      default: true,
    },
    lastReset: {
      type: Date,
      default: Date.now,
    },
  },
  
  // Campaign Management
  campaigns: [{
    type: Schema.Types.ObjectId,
    ref: 'Campaign',
  }],
  
  // Performance Metrics
  metrics: {
    totalCampaigns: {
      type: Number,
      default: 0,
    },
    activeCampaigns: {
      type: Number,
      default: 0,
    },
    completedCampaigns: {
      type: Number,
      default: 0,
    },
    totalApplications: {
      type: Number,
      default: 0,
    },
    averageSubmissionRate: {
      type: Number,
      default: 0,
    },
    totalSpent: {
      type: Number,
      default: 0,
    },
  },
  
  // Billing Information
  billingInfo: {
    stripeCustomerId: String,
    paymentMethods: [{
      id: String,
      type: String,
      last4: String,
      brand: String,
      isDefault: Boolean,
    }],
    billingAddress: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
    },
  },
  
  // Security
  lastLogin: Date,
  
  loginAttempts: {
    type: Number,
    default: 0,
  },
  
  lockUntil: Date,
  
  // Email Verification
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Password Reset
  passwordResetToken: String,
  passwordResetExpires: Date,
  
  // Admin Fields
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
  },
  
  approvedAt: Date,
  
  rejectionReason: String,
  
  // Preferences
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true,
    },
    marketingEmails: {
      type: Boolean,
      default: true,
    },
    weeklyReports: {
      type: Boolean,
      default: true,
    },
  },
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better performance
brandSchema.index({ email: 1 });
brandSchema.index({ brandName: 1 });
brandSchema.index({ 'subscription.status': 1 });
brandSchema.index({ isApproved: 1, isBlocked: 1 });
brandSchema.index({ createdAt: -1 });

// Virtual for account lock status
brandSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Virtual for full contact name
brandSchema.virtual('contactFullName').get(function() {
  return `${this.contactPerson.firstName} ${this.contactPerson.lastName}`;
});

// Virtual for campaigns remaining
brandSchema.virtual('campaignsRemaining').get(function() {
  const totalAllowed = this.planLimits.campaignsAllowed + this.planLimits.extraCampaignsAllowed;
  return Math.max(0, totalAllowed - this.planLimits.campaignsUsed);
});

// Pre-save middleware to hash password
brandSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.password = await bcrypt.hash(this.password, saltRounds);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
brandSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
brandSchema.methods.incLoginAttempts = function() {
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 };
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
brandSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Method to check if can create campaign
brandSchema.methods.canCreateCampaign = function() {
  return this.campaignsRemaining > 0;
};

// Method to reset monthly usage
brandSchema.methods.resetMonthlyUsage = function() {
  if (this.planLimits.monthlyReset) {
    this.planLimits.campaignsUsed = 0;
    this.planLimits.lastReset = new Date();
    return this.save();
  }
};

// Static method to find by email
brandSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to find approved brands
brandSchema.statics.findApproved = function() {
  return this.find({ isApproved: true, isBlocked: false });
};

// Static method to find pending approval
brandSchema.statics.findPendingApproval = function() {
  return this.find({ isApproved: false, isBlocked: false });
};

export default mongoose.model('Brand', brandSchema);
