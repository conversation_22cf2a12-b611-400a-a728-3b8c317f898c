import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import validator from 'validator';

const { Schema } = mongoose;

// User schema with improved structure and validation
const userSchema = new Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    minlength: [2, 'Name must be at least 2 characters long'],
    maxlength: [50, 'Name cannot exceed 50 characters'],
  },
  
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    validate: [validator.isEmail, 'Please provide a valid email address'],
    index: true,
  },
  
  password: {
    type: String,
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false, // Don't include password in queries by default
  },
  
  // Account Status
  isVerified: {
    type: Boolean,
    default: false,
  },
  
  isBlocked: {
    type: Boolean,
    default: false,
  },
  
  // OAuth Information
  isGoogleUser: {
    type: Boolean,
    default: false,
  },
  
  googleId: {
    type: String,
    sparse: true, // Allow multiple null values
  },
  
  // Profile Information
  avatar: {
    url: String,
    publicId: String,
  },
  
  bio: {
    type: String,
    maxlength: [500, 'Bio cannot exceed 500 characters'],
    trim: true,
  },
  
  // Contact Information
  phone: {
    type: String,
    validate: {
      validator: function(v) {
        return !v || validator.isMobilePhone(v);
      },
      message: 'Please provide a valid phone number',
    },
  },
  
  // Address Information
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: {
      type: String,
      validate: {
        validator: function(v) {
          return !v || /^\d{5}(-\d{4})?$/.test(v);
        },
        message: 'Please provide a valid ZIP code',
      },
    },
    country: {
      type: String,
      default: 'US',
    },
  },
  
  // Social Media Information
  socialMedia: {
    instagram: {
      username: String,
      followers: {
        type: Number,
        min: 0,
        default: 0,
      },
      verified: {
        type: Boolean,
        default: false,
      },
    },
    tiktok: {
      username: String,
      followers: {
        type: Number,
        min: 0,
        default: 0,
      },
      verified: {
        type: Boolean,
        default: false,
      },
    },
    youtube: {
      username: String,
      subscribers: {
        type: Number,
        min: 0,
        default: 0,
      },
      verified: {
        type: Boolean,
        default: false,
      },
    },
  },
  
  // Campaign Related
  appliedCampaigns: [{
    type: Schema.Types.ObjectId,
    ref: 'CampaignApplication',
  }],
  
  // Performance Metrics
  performanceScore: {
    type: Number,
    min: 0,
    max: 100,
    default: null, // null until they have enough campaigns
  },
  
  totalCampaigns: {
    type: Number,
    default: 0,
    min: 0,
  },
  
  completedCampaigns: {
    type: Number,
    default: 0,
    min: 0,
  },
  
  // Referral System
  referralCode: {
    type: String,
    unique: true,
    sparse: true,
  },
  
  referredBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  
  referralCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  
  // Points and Rewards
  points: {
    type: Number,
    default: 0,
    min: 0,
  },
  
  // Account Settings
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true,
    },
    smsNotifications: {
      type: Boolean,
      default: false,
    },
    marketingEmails: {
      type: Boolean,
      default: true,
    },
  },
  
  // Security
  lastLogin: Date,
  
  loginAttempts: {
    type: Number,
    default: 0,
  },
  
  lockUntil: Date,
  
  // Email Verification
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Password Reset
  passwordResetToken: String,
  passwordResetExpires: Date,
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ referralCode: 1 });
userSchema.index({ 'socialMedia.instagram.username': 1 });
userSchema.index({ 'socialMedia.tiktok.username': 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ performanceScore: -1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash password if it's modified and exists
  if (!this.isModified('password') || !this.password) {
    return next();
  }
  
  try {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.password = await bcrypt.hash(this.password, saltRounds);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Method to generate referral code
userSchema.methods.generateReferralCode = function() {
  const code = Math.random().toString(36).substring(2, 8).toUpperCase();
  this.referralCode = code;
  return code;
};

// Method to update performance score
userSchema.methods.updatePerformanceScore = function(score) {
  this.performanceScore = Math.max(0, Math.min(100, score));
  return this.save();
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to find active users
userSchema.statics.findActive = function() {
  return this.find({ isBlocked: false, isVerified: true });
};

// Static method to find top performers
userSchema.statics.findTopPerformers = function(limit = 10) {
  return this.find({
    performanceScore: { $ne: null },
    isBlocked: false,
    isVerified: true
  })
  .sort({ performanceScore: -1 })
  .limit(limit);
};

export default mongoose.model('User', userSchema);
