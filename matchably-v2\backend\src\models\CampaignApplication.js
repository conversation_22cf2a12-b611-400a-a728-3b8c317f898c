import mongoose from 'mongoose';
import validator from 'validator';

const { Schema } = mongoose;

// Campaign Application schema
const campaignApplicationSchema = new Schema({
  // References
  campaign: {
    type: Schema.Types.ObjectId,
    ref: 'Campaign',
    required: [true, 'Campaign reference is required'],
    index: true,
  },
  
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User reference is required'],
    index: true,
  },
  
  // Application Details
  applicationText: {
    type: String,
    required: [true, 'Application message is required'],
    trim: true,
    minlength: [50, 'Application message must be at least 50 characters long'],
    maxlength: [1000, 'Application message cannot exceed 1000 characters'],
  },
  
  // Social Media Information at time of application
  socialMediaSnapshot: {
    instagram: {
      username: String,
      followers: Number,
      engagementRate: Number,
      verified: Boolean,
      profileUrl: String,
    },
    tiktok: {
      username: String,
      followers: Number,
      engagementRate: Number,
      verified: Boolean,
      profileUrl: String,
    },
    youtube: {
      username: String,
      subscribers: Number,
      averageViews: Number,
      verified: Bo<PERSON>an,
      channelUrl: String,
    },
  },
  
  // Portfolio/Previous Work
  portfolio: [{
    platform: {
      type: String,
      enum: ['Instagram', 'TikTok', 'YouTube'],
      required: true,
    },
    url: {
      type: String,
      required: true,
      validate: [validator.isURL, 'Please provide a valid URL'],
    },
    description: {
      type: String,
      maxlength: [200, 'Description cannot exceed 200 characters'],
    },
    metrics: {
      views: Number,
      likes: Number,
      comments: Number,
      shares: Number,
    },
  }],
  
  // Application Status
  status: {
    type: String,
    enum: ['Pending', 'Approved', 'Rejected', 'Withdrawn'],
    default: 'Pending',
    index: true,
  },
  
  // Review Information
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Brand',
  },
  
  reviewedAt: Date,
  
  reviewNotes: {
    type: String,
    maxlength: [500, 'Review notes cannot exceed 500 characters'],
  },
  
  rejectionReason: {
    type: String,
    enum: [
      'Insufficient followers',
      'Poor engagement rate',
      'Content quality',
      'Brand mismatch',
      'Geographic location',
      'Previous work quality',
      'Application quality',
      'Other'
    ],
  },
  
  rejectionDetails: {
    type: String,
    maxlength: [500, 'Rejection details cannot exceed 500 characters'],
  },
  
  // Tracking Information (for approved applications)
  tracking: {
    productShipped: {
      type: Boolean,
      default: false,
    },
    shippingDate: Date,
    trackingNumber: String,
    trackingUrl: String,
    deliveryDate: Date,
    deliveryConfirmed: {
      type: Boolean,
      default: false,
    },
  },
  
  // Content Submission
  contentSubmission: {
    submitted: {
      type: Boolean,
      default: false,
    },
    submissionDate: Date,
    urls: [{
      platform: String,
      url: String,
      metrics: {
        views: Number,
        likes: Number,
        comments: Number,
        shares: Number,
        saves: Number,
      },
    }],
    allowBrandReuse: {
      type: Boolean,
      default: false,
    },
    notes: String,
  },
  
  // Performance Tracking
  performance: {
    onTime: {
      type: Boolean,
      default: null, // null = not determined yet
    },
    submissionDeadline: Date,
    daysToSubmission: Number,
    qualityScore: {
      type: Number,
      min: 1,
      max: 5,
    },
    brandSatisfaction: {
      type: Number,
      min: 1,
      max: 5,
    },
  },
  
  // Communication Log
  communications: [{
    type: {
      type: String,
      enum: ['Email', 'Platform Message', 'Phone', 'Other'],
    },
    direction: {
      type: String,
      enum: ['Inbound', 'Outbound'],
    },
    subject: String,
    message: String,
    timestamp: {
      type: Date,
      default: Date.now,
    },
    sentBy: {
      type: Schema.Types.ObjectId,
      refPath: 'communications.sentByModel',
    },
    sentByModel: {
      type: String,
      enum: ['User', 'Brand', 'Admin'],
    },
  }],
  
  // Extension Requests
  extensionRequests: [{
    requestedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    requestDate: {
      type: Date,
      default: Date.now,
    },
    reason: {
      type: String,
      required: true,
      maxlength: [500, 'Reason cannot exceed 500 characters'],
    },
    requestedExtension: {
      type: Number, // days
      required: true,
      min: 1,
      max: 30,
    },
    status: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected'],
      default: 'Pending',
    },
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: 'Brand',
    },
    reviewedAt: Date,
    reviewNotes: String,
  }],
  
  // Metadata
  applicationSource: {
    type: String,
    enum: ['Website', 'Mobile App', 'API'],
    default: 'Website',
  },
  
  ipAddress: String,
  userAgent: String,
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Compound indexes for better performance
campaignApplicationSchema.index({ campaign: 1, user: 1 }, { unique: true });
campaignApplicationSchema.index({ campaign: 1, status: 1 });
campaignApplicationSchema.index({ user: 1, status: 1 });
campaignApplicationSchema.index({ status: 1, createdAt: -1 });
campaignApplicationSchema.index({ reviewedAt: -1 });

// Virtual for days since application
campaignApplicationSchema.virtual('daysSinceApplication').get(function() {
  const now = new Date();
  const diffTime = now - this.createdAt;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for days until content deadline
campaignApplicationSchema.virtual('daysUntilDeadline').get(function() {
  if (!this.performance.submissionDeadline) return null;
  const now = new Date();
  const deadline = new Date(this.performance.submissionDeadline);
  const diffTime = deadline - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for application summary
campaignApplicationSchema.virtual('summary').get(function() {
  return {
    id: this._id,
    status: this.status,
    appliedAt: this.createdAt,
    reviewedAt: this.reviewedAt,
    contentSubmitted: this.contentSubmission.submitted,
    onTime: this.performance.onTime,
  };
});

// Pre-save middleware to calculate performance metrics
campaignApplicationSchema.pre('save', function(next) {
  // Calculate days to submission if content was submitted
  if (this.contentSubmission.submitted && this.contentSubmission.submissionDate) {
    const diffTime = this.contentSubmission.submissionDate - this.createdAt;
    this.performance.daysToSubmission = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    // Determine if submission was on time
    if (this.performance.submissionDeadline) {
      this.performance.onTime = this.contentSubmission.submissionDate <= this.performance.submissionDeadline;
    }
  }
  
  next();
});

// Static method to find by campaign
campaignApplicationSchema.statics.findByCampaign = function(campaignId, status = null) {
  const query = { campaign: campaignId };
  if (status) query.status = status;
  return this.find(query).populate('user', 'name email socialMedia');
};

// Static method to find by user
campaignApplicationSchema.statics.findByUser = function(userId, status = null) {
  const query = { user: userId };
  if (status) query.status = status;
  return this.find(query).populate('campaign', 'title brandName status applicationDeadline');
};

// Static method to find pending applications
campaignApplicationSchema.statics.findPending = function(limit = 50) {
  return this.find({ status: 'Pending' })
    .populate('campaign', 'title brandName')
    .populate('user', 'name email')
    .sort({ createdAt: 1 })
    .limit(limit);
};

// Method to approve application
campaignApplicationSchema.methods.approve = function(reviewedBy, notes = '') {
  this.status = 'Approved';
  this.reviewedBy = reviewedBy;
  this.reviewedAt = new Date();
  this.reviewNotes = notes;
  
  // Set submission deadline (14 days from approval)
  const deadline = new Date();
  deadline.setDate(deadline.getDate() + 14);
  this.performance.submissionDeadline = deadline;
  
  return this.save();
};

// Method to reject application
campaignApplicationSchema.methods.reject = function(reviewedBy, reason, details = '') {
  this.status = 'Rejected';
  this.reviewedBy = reviewedBy;
  this.reviewedAt = new Date();
  this.rejectionReason = reason;
  this.rejectionDetails = details;
  
  return this.save();
};

// Method to submit content
campaignApplicationSchema.methods.submitContent = function(urls, allowReuse = false, notes = '') {
  this.contentSubmission.submitted = true;
  this.contentSubmission.submissionDate = new Date();
  this.contentSubmission.urls = urls;
  this.contentSubmission.allowBrandReuse = allowReuse;
  this.contentSubmission.notes = notes;
  
  return this.save();
};

export default mongoose.model('CampaignApplication', campaignApplicationSchema);
