const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 2340;

// Mock user database
const mockUsers = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: '$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2', // SecurePass2024!@#$
    isVerified: true,
    blocked: false
  },
  {
    id: 2,
    name: 'Demo Brand User',
    email: '<EMAIL>',
    password: '$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2', // DemoUser2024!@#
    isVerified: true,
    blocked: false
  },
  {
    id: 3,
    name: 'Test Brand',
    email: '<EMAIL>',
    password: '$2b$10$kthSu.4WHdClXAhEdcEbTeChxrfKC0hqidI3sDJv3uDTTa56KjbR2', // TestBrand2024!@#
    isVerified: true,
    blocked: false
  }
];

// Mock campaigns data
const mockCampaigns = [
  {
    id: 1,
    title: 'Glow Serum Ampoule',
    status: 'active',
    description: 'Promote our new vitamin C glow serum to skincare enthusiasts',
    deadline: '2025-06-03',
    budget: 5000,
    applicants: 6,
    submissions: 7,
    submissionRate: 78
  },
  {
    id: 2,
    title: 'Vita Bright Toner Pack',
    status: 'draft',
    description: 'Launch campaign for our new vitamin toner pack',
    deadline: null,
    budget: 3000,
    applicants: 0,
    submissions: 0,
    submissionRate: 0
  }
];

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

app.use(express.json());

// Helper functions
const generateToken = (payload) => {
  return jwt.sign(payload, 'test-secret-key', { expiresIn: '24h' });
};

const verifyToken = (token) => {
  return jwt.verify(token.replace('Bearer ', ''), 'test-secret-key');
};

// Routes
app.get('/', (req, res) => {
  res.json({
    app: 'Matchably Test Server',
    timestamp: new Date().toISOString(),
    status: 'running',
    routes: {
      auth: '/api/auth',
      brand: '/api/brand',
      health: '/health'
    }
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    database: 'mock'
  });
});

// Auth routes
app.post('/api/auth/signin', async (req, res) => {
  const { email, password } = req.body;
  
  console.log('🔍 Login attempt:', email);
  
  try {
    const user = mockUsers.find(u => u.email === email);
    if (!user) {
      return res.json({ status: "failed", message: "User not found" });
    }
    
    if (user.blocked) {
      return res.json({ status: "failed", message: "User is blocked" });
    }
    
    if (!user.isVerified) {
      return res.json({ status: "failed", message: "Please verify your email first" });
    }
    
    // For testing, accept any password that matches the demo passwords
    const validPasswords = ['SecurePass2024!@#$', 'DemoUser2024!@#', 'TestBrand2024!@#'];
    const isValidPassword = validPasswords.includes(password) || await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      return res.json({ status: "failed", message: "Incorrect password" });
    }
    
    const token = generateToken({ email: user.email, id: user.id });
    console.log('✅ Login successful for:', email);
    
    res.json({ 
      status: "success", 
      message: "Login successful", 
      token,
      user: {
        name: user.name,
        email: user.email
      }
    });
  } catch (error) {
    console.error('❌ Login error:', error);
    res.json({ status: "failed", message: "Something went wrong" });
  }
});

// Admin routes
app.post('/api/admin/auth', async (req, res) => {
  const { username, password } = req.body;

  console.log('🔍 Admin login attempt:', username);

  try {
    // Admin credentials from .env
    const ADMIN_USER = '<EMAIL>';
    const ADMIN_PASS = 'Wjdalsdnd0145!!';

    if (username !== ADMIN_USER) {
      return res.json({
        status: "failed",
        message: "Username or password incorrect",
      });
    }

    if (password !== ADMIN_PASS) {
      return res.json({
        status: "failed",
        message: "Username or password incorrect",
      });
    }

    const token = generateToken({ username, role: "admin" });
    console.log('✅ Admin login successful for:', username);

    res.json({
      status: "success",
      message: "Login successful",
      token,
    });
  } catch (error) {
    console.error('❌ Admin login error:', error);
    res.json({ status: "failed", message: "Something went wrong" });
  }
});

app.get('/api/admin/verify', (req, res) => {
  try {
    const token = req.headers.authorization;
    if (!token) {
      return res.json({ status: "failed", message: "No token provided" });
    }

    const decoded = verifyToken(token);

    if (decoded.role !== "admin" || decoded.username !== '<EMAIL>') {
      return res.json({ status: "failed", message: "Unauthorized" });
    }

    res.json({
      status: "success",
      message: "Verified",
    });
  } catch (error) {
    console.error('❌ Admin verify error:', error);
    res.json({ status: "failed", message: "Invalid or expired token" });
  }
});

// Brand routes
app.get('/api/brand/campaigns', (req, res) => {
  try {
    const { empty } = req.query;
    
    // Check authorization
    const token = req.headers.authorization;
    if (!token) {
      return res.json({ status: "failed", message: "No token provided" });
    }
    
    try {
      verifyToken(token);
    } catch (err) {
      return res.json({ status: "failed", message: "Invalid token" });
    }
    
    if (empty === 'true') {
      return res.json({
        status: "success",
        campaigns: []
      });
    }
    
    res.json({
      status: "success",
      campaigns: mockCampaigns
    });
  } catch (error) {
    console.error('❌ Campaigns error:', error);
    res.json({ status: "failed", message: "Failed to fetch campaigns" });
  }
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Test server running on port', PORT);
  console.log('📡 Health check: http://localhost:' + PORT + '/health');
  console.log('🔗 API endpoints: http://localhost:' + PORT + '/api');
  console.log('');
  console.log('📋 BRAND TEST CREDENTIALS:');
  console.log('Email: <EMAIL>');
  console.log('Password: SecurePass2024!@#$');
  console.log('');
  console.log('Email: <EMAIL>');
  console.log('Password: DemoUser2024!@#');
  console.log('');
  console.log('👑 ADMIN TEST CREDENTIALS:');
  console.log('Username: <EMAIL>');
  console.log('Password: Wjdalsdnd0145!!');
  console.log('');
  console.log('🔗 Brand login: http://localhost:3000/brand-auth');
  console.log('🔗 Admin login: http://localhost:3000/admin/auth');
});
