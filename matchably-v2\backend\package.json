{"name": "matchably-v2-backend", "version": "2.0.0", "description": "Modern backend for Matchably UGC platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ugc", "influencer", "marketing", "campaigns", "matchably"], "author": "Matchably Team", "license": "MIT", "dependencies": {"express": "^4.21.2", "mongoose": "^8.14.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "multer": "^1.4.4", "cloudinary": "^1.41.3", "nodemailer": "^6.10.0", "stripe": "^17.5.0", "validator": "^13.15.0", "express-validator": "^7.2.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "uuid": "^11.0.4", "sharp": "^0.33.5", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^29.7.0", "supertest": "^7.0.0", "eslint": "^9.21.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "^3.4.2", "@types/node": "^22.10.5"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/config/*.js"]}, "eslintConfig": {"env": {"node": true, "es2022": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error"}}}