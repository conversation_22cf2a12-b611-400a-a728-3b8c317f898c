import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Shield, Home, LogIn } from 'lucide-react'
import { Helmet } from 'react-helmet-async'

const Unauthorized: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Unauthorized - Matchably</title>
        <meta name="description" content="You don't have permission to access this page." />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-background-primary">
        <div className="max-w-md w-full text-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Shield Icon */}
            <motion.div
              className="w-24 h-24 mx-auto mb-6 rounded-full bg-error-500/20 flex items-center justify-center"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Shield className="w-12 h-12 text-error-500" />
            </motion.div>

            <h1 className="text-3xl font-bold text-text-primary mb-4">
              Access Denied
            </h1>
            
            <p className="text-text-secondary mb-8">
              You don't have permission to access this page. 
              Please log in with the appropriate account or contact support if you believe this is an error.
            </p>

            <div className="space-y-4">
              <Link
                to="/login"
                className="btn-primary w-full flex items-center justify-center space-x-2"
              >
                <LogIn className="w-5 h-5" />
                <span>Login</span>
              </Link>
              
              <Link
                to="/"
                className="btn-secondary w-full flex items-center justify-center space-x-2"
              >
                <Home className="w-5 h-5" />
                <span>Go Home</span>
              </Link>
            </div>

            {/* Floating elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <motion.div
                className="absolute top-1/3 left-1/3 w-20 h-20 bg-error-500/10 rounded-full blur-xl"
                animate={{
                  x: [0, 25, 0],
                  y: [0, -15, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>
    </>
  )
}

export default Unauthorized
