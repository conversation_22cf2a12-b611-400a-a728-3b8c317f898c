const axios = require('axios');

async function testLogin() {
  console.log('🔍 Testing Brand Login...');
  
  try {
    const response = await axios.post('http://localhost:2340/api/auth/signin', {
      email: '<EMAIL>',
      password: 'SecurePass2024!@#$'
    });
    
    if (response.data.status === 'success') {
      console.log('✅ LOGIN SUCCESSFUL!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔐 Password: SecurePass2024!@#$');
      console.log('🔗 Login URL: http://localhost:3001/brand-auth');
      console.log('🎯 Token generated successfully');
      console.log('\n🚀 You can now access the brand dashboard!');
    } else {
      console.log('❌ Login failed:', response.data.message);
    }
  } catch (error) {
    console.log('❌ Connection error:', error.message);
    console.log('💡 Make sure the backend server is running on port 2340');
  }
}

testLogin();
