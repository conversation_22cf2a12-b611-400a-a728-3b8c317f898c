import mongoose from 'mongoose';
import { logger } from '../utils/logger.js';

// MongoDB connection options
const options = {
  // Connection settings
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  bufferMaxEntries: 0, // Disable mongoose buffering
  bufferCommands: false, // Disable mongoose buffering
  
  // Replica set settings
  retryWrites: true,
  w: 'majority',
  
  // Compression
  compressors: ['zlib'],
  
  // Authentication
  authSource: 'admin',
};

/**
 * Connect to MongoDB database
 */
export const connectDB = async () => {
  try {
    const mongoUrl = process.env.MONGO_URL;
    
    if (!mongoUrl) {
      throw new Error('MONGO_URL environment variable is not defined');
    }

    logger.info('🔍 Connecting to MongoDB...');
    
    const conn = await mongoose.connect(mongoUrl, options);
    
    logger.info(`✅ MongoDB Connected: ${conn.connection.host}`);
    logger.info(`📊 Database: ${conn.connection.name}`);
    
    // Connection event listeners
    mongoose.connection.on('connected', () => {
      logger.info('📡 Mongoose connected to MongoDB');
    });
    
    mongoose.connection.on('error', (err) => {
      logger.error('❌ Mongoose connection error:', err);
    });
    
    mongoose.connection.on('disconnected', () => {
      logger.warn('⚠️ Mongoose disconnected from MongoDB');
    });
    
    // Handle application termination
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('🛑 Mongoose connection closed through app termination');
      process.exit(0);
    });
    
    return conn;
    
  } catch (error) {
    logger.error('❌ MongoDB connection failed:', error.message);
    
    // Log specific connection errors
    if (error.name === 'MongoNetworkError') {
      logger.error('🌐 Network error - check your MongoDB connection string and network connectivity');
    } else if (error.name === 'MongoAuthenticationError') {
      logger.error('🔐 Authentication error - check your MongoDB credentials');
    } else if (error.name === 'MongoServerSelectionError') {
      logger.error('🖥️ Server selection error - MongoDB server may be down or unreachable');
    }
    
    process.exit(1);
  }
};

/**
 * Close database connection
 */
export const closeDB = async () => {
  try {
    await mongoose.connection.close();
    logger.info('🔌 Database connection closed');
  } catch (error) {
    logger.error('❌ Error closing database connection:', error);
  }
};

/**
 * Check database connection status
 */
export const getDBStatus = () => {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
  };
  
  return {
    status: states[mongoose.connection.readyState],
    host: mongoose.connection.host,
    name: mongoose.connection.name,
    port: mongoose.connection.port,
  };
};

/**
 * Database health check
 */
export const healthCheck = async () => {
  try {
    const status = getDBStatus();
    
    if (status.status !== 'connected') {
      throw new Error(`Database is ${status.status}`);
    }
    
    // Ping the database
    await mongoose.connection.db.admin().ping();
    
    return {
      status: 'healthy',
      connection: status,
      timestamp: new Date().toISOString(),
    };
    
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

// Export mongoose for direct access if needed
export { mongoose };
