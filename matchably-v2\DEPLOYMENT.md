# Deployment Guide - Matchably v2

This guide covers various deployment options for the Matchably v2 platform.

## 🚀 Quick Deploy Options

### 1. Docker Compose (Recommended for Development)

```bash
# Clone the repository
git clone <repository-url>
cd matchably-v2

# Copy environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit environment files with your configuration
# Then start all services
docker-compose up -d
```

### 2. Manual Deployment

#### Prerequisites
- Node.js 18+
- MongoDB 5.0+
- Redis (optional)
- Cloudinary account
- Stripe account

#### Backend Deployment

```bash
cd backend

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with production values

# Build and start
npm run build
npm start
```

#### Frontend Deployment

```bash
cd frontend

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with production values

# Build
npm run build

# Serve with nginx or any static file server
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using AWS ECS with Fargate

1. **Build and push Docker images**
   ```bash
   # Build images
   docker build -t matchably-backend ./backend
   docker build -t matchably-frontend ./frontend
   
   # Tag for ECR
   docker tag matchably-backend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/matchably-backend:latest
   docker tag matchably-frontend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/matchably-frontend:latest
   
   # Push to ECR
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/matchably-backend:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/matchably-frontend:latest
   ```

2. **Set up MongoDB Atlas**
   - Create MongoDB Atlas cluster
   - Configure network access
   - Get connection string

3. **Create ECS Task Definitions**
   - Backend task with environment variables
   - Frontend task
   - Configure load balancer

4. **Set up Application Load Balancer**
   - Route `/api/*` to backend
   - Route `/*` to frontend

#### Using AWS Lambda (Serverless)

```bash
# Install Serverless Framework
npm install -g serverless

# Deploy backend
cd backend
serverless deploy

# Deploy frontend to S3 + CloudFront
cd frontend
npm run build
aws s3 sync dist/ s3://your-bucket-name
```

### Google Cloud Platform

#### Using Google Cloud Run

```bash
# Build and deploy backend
cd backend
gcloud builds submit --tag gcr.io/PROJECT-ID/matchably-backend
gcloud run deploy matchably-backend --image gcr.io/PROJECT-ID/matchably-backend --platform managed

# Build and deploy frontend
cd frontend
gcloud builds submit --tag gcr.io/PROJECT-ID/matchably-frontend
gcloud run deploy matchably-frontend --image gcr.io/PROJECT-ID/matchably-frontend --platform managed
```

### Vercel (Frontend) + Railway (Backend)

#### Frontend on Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy frontend
cd frontend
vercel --prod
```

#### Backend on Railway
1. Connect GitHub repository to Railway
2. Set environment variables
3. Deploy automatically on push

### Heroku

#### Backend on Heroku
```bash
# Install Heroku CLI
# Create Heroku app
heroku create matchably-backend

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your-mongodb-uri
heroku config:set JWT_SECRET=your-jwt-secret

# Deploy
git subtree push --prefix backend heroku main
```

#### Frontend on Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Build and deploy
cd frontend
npm run build
netlify deploy --prod --dir=dist
```

## 🔧 Environment Configuration

### Production Environment Variables

#### Backend (.env)
```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://username:<EMAIL>/matchably-v2
JWT_SECRET=your-super-secure-jwt-secret-256-bits-minimum
JWT_EXPIRES_IN=7d
FRONTEND_URL=https://your-domain.com
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
STRIPE_SECRET_KEY=sk_live_your-stripe-secret
EMAIL_HOST=smtp.sendgrid.net
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key
```

#### Frontend (.env)
```env
VITE_API_URL=https://api.your-domain.com
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
```

## 🔒 Security Checklist

### Pre-deployment Security
- [ ] Change all default passwords
- [ ] Use strong JWT secrets (256+ bits)
- [ ] Enable HTTPS/SSL certificates
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable security headers
- [ ] Configure CSP headers
- [ ] Set up monitoring and logging
- [ ] Enable database authentication
- [ ] Configure firewall rules

### SSL/TLS Setup

#### Using Let's Encrypt with Nginx
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com -d api.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring & Logging

### Application Monitoring
- Set up health check endpoints
- Configure uptime monitoring
- Set up error tracking (Sentry)
- Monitor performance metrics
- Set up log aggregation

### Database Monitoring
- Monitor connection pool
- Track query performance
- Set up backup schedules
- Monitor disk usage

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          # Your deployment script here
```

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check FRONTEND_URL in backend .env
   - Verify CORS configuration

2. **Database Connection Issues**
   - Verify MongoDB URI
   - Check network access rules
   - Ensure database is running

3. **File Upload Issues**
   - Verify Cloudinary credentials
   - Check file size limits
   - Ensure proper CORS for Cloudinary

4. **Payment Issues**
   - Verify Stripe keys (test vs live)
   - Check webhook endpoints
   - Ensure HTTPS for production

### Performance Optimization

1. **Backend**
   - Enable compression
   - Use Redis for caching
   - Optimize database queries
   - Set up CDN for static assets

2. **Frontend**
   - Enable gzip compression
   - Use lazy loading
   - Optimize images
   - Implement code splitting

## 📞 Support

For deployment support:
- Email: <EMAIL>
- Documentation: https://docs.matchably.com
- Discord: https://discord.gg/matchably
